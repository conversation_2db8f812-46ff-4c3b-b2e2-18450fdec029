import React, { useState, useEffect } from "react";
import axios from "axios";

const ViewCounter = ({ videoId }) => {
  const [views, setViews] = useState(null);
  const API_KEY = "AIzaSyCFlFZiWXYrt245BwstDQCe7o8ki1NGm7w"; // Replace with your API key [[4]]

  useEffect(() => {
    const fetchViewCount = async () => {
      try {
        const response = await axios.get(
          `https://www.googleapis.com/youtube/v3/videos?part=statistics&id=${videoId}&key=${API_KEY}`
        );
        const viewCount = response.data.items[0].statistics.viewCount;
        setViews(viewCount);
      } catch (error) {
        console.error("Error fetching view count:", error);
      }
    };

    fetchViewCount();
  }, [videoId]);

  return (
    <div>
      {views ? (
        <p>View Count: {views}</p>
      ) : (
        <p>Loading views...</p>
      )}
    </div>
  );
};

export default ViewCounter;